<!-- 动作参数输入组件 -->
<script lang="ts">
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { Button } from "$lib/components/ui/button";
  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "$lib/components/ui/select";
  import { Checkbox } from "$lib/components/ui/checkbox";

  import { open } from "@tauri-apps/plugin-dialog";
  import type { ActionParamDefinition } from "$lib/types/action";

  interface Props {
    param: ActionParamDefinition;
    value: any;
    onValueChange: (value: any) => void;
    error?: string;
    t?: (key: string) => string; // i18n翻译函数
  }

  let {
    param,
    value,
    onValueChange,
    error,
    t = (key: string) => key,
  }: Props = $props();

  // 获取显示名称
  function getDisplayName(): string {
    const baseName = t ? t(param.nameKey) : param.nameKey;
    // 如果是数值类型且有范围限制，添加范围信息到名称中
    if (
      (param.type === "number" ||
        param.type === "range" ||
        param.type === "duration") &&
      param.min !== undefined &&
      param.max !== undefined
    ) {
      return `${baseName} (${param.min} - ${param.max})`;
    }
    return baseName;
  }

  // 获取描述
  function getDescription(): string {
    return param.descriptionKey
      ? t
        ? t(param.descriptionKey)
        : param.descriptionKey
      : "";
  }

  // 文件选择处理
  async function handleFileSelect() {
    try {
      const selected = await open({
        multiple: false,
        filters: [
          {
            name: "All Files",
            extensions: ["*"],
          },
        ],
      });

      if (selected && typeof selected === "string") {
        onValueChange(selected);
      }
    } catch (error) {
      console.error("File selection error:", error);
    }
  }

  // 图片文件选择处理
  async function handleImageFileSelect() {
    try {
      const selected = await open({
        multiple: false,
        filters: [
          {
            name: "图片文件",
            extensions: [
              "jpg",
              "jpeg",
              "png",
              "bmp",
              "gif",
              "webp",
              "tiff",
              "tga",
            ],
          },
        ],
      });

      if (selected && typeof selected === "string") {
        onValueChange(selected);
      }
    } catch (error) {
      console.error("Image file selection error:", error);
    }
  }

  // 音频文件选择处理
  async function handleAudioFileSelect() {
    try {
      const selected = await open({
        multiple: false,
        filters: [
          {
            name: "音频文件",
            extensions: ["mp3", "wav", "aac", "flac", "m4a", "ogg", "wma"],
          },
        ],
      });

      if (selected && typeof selected === "string") {
        onValueChange(selected);
      }
    } catch (error) {
      console.error("Audio file selection error:", error);
    }
  }

  // 选择框值变化处理
  function handleSelectChange(selectedValue: string) {
    onValueChange(selectedValue);
  }

  // 复选框值变化处理
  function handleCheckboxChange(checked: boolean) {
    onValueChange(checked);
  }
</script>

<div class="space-y-2">
  {#if param.type !== "image-file"}
    <Label for={param.key} class="text-sm font-medium">
      {getDisplayName()}
      {#if param.required}
        <span class="text-red-500">*</span>
      {/if}
    </Label>

    {#if getDescription()}
      <p class="text-xs text-muted-foreground">{getDescription()}</p>
    {/if}
  {/if}

  <!-- 根据参数类型渲染不同的输入控件 -->
  {#if param.type === "string" || param.type === "duration"}
    <Input
      id={param.key}
      type={param.type === "duration" ? "number" : "text"}
      {value}
      oninput={(e) => onValueChange(e.currentTarget.value)}
      placeholder={param.type === "duration" ? "秒" : ""}
      min={param.min}
      max={param.max}
      step={param.step}
      class={error ? "border-red-500" : ""}
    />
  {:else if param.type === "number" || param.type === "range"}
    <Input
      id={param.key}
      type="number"
      {value}
      oninput={(e) => onValueChange(Number(e.currentTarget.value))}
      min={param.min}
      max={param.max}
      step={param.step}
      class={error ? "border-red-500" : ""}
    />
  {:else if param.type === "boolean"}
    <div class="flex items-center space-x-2">
      <Checkbox
        id={param.key}
        checked={value}
        onCheckedChange={handleCheckboxChange}
      />
      <Label for={param.key} class="text-sm">{getDisplayName()}</Label>
    </div>
  {:else if param.type === "select"}
    <Select {value} onValueChange={handleSelectChange}>
      <SelectTrigger class={error ? "border-red-500" : ""}>
        <SelectValue placeholder="请选择..." />
      </SelectTrigger>
      <SelectContent>
        {#each param.options || [] as option}
          <SelectItem value={option.value}>
            {t ? t(option.labelKey) : option.labelKey}
          </SelectItem>
        {/each}
      </SelectContent>
    </Select>
  {:else if param.type === "file"}
    <div class="flex space-x-2">
      <Input
        id={param.key}
        type="text"
        {value}
        oninput={(e) => onValueChange(e.currentTarget.value)}
        placeholder="选择文件..."
        readonly
        class={error ? "border-red-500" : ""}
      />
      <Button type="button" variant="outline" onclick={handleFileSelect}>
        浏览
      </Button>
    </div>
  {:else if param.type === "image-file"}
    <div class="flex items-center space-x-2">
      <span class="text-sm text-muted-foreground whitespace-nowrap">
        {getDisplayName()}
      </span>
      <Button
        type="button"
        variant="default"
        onclick={handleImageFileSelect}
        class="flex-shrink-0"
      >
        选择图片文件
      </Button>
      {#if value}
        <span class="text-xs text-muted-foreground truncate" title={value}>
          {value.split(/[/\\]/).pop()}
        </span>
      {/if}
    </div>
  {:else if param.type === "audio-file"}
    <div class="flex items-center space-x-2">
      <span class="text-sm text-muted-foreground whitespace-nowrap">
        {getDisplayName()}
      </span>
      <Button
        type="button"
        variant="default"
        onclick={handleAudioFileSelect}
        class="flex-shrink-0"
      >
        选择音频文件
      </Button>
      {#if value}
        <span class="text-xs text-muted-foreground truncate" title={value}>
          {value.split(/[/\\]/).pop()}
        </span>
      {/if}
    </div>
  {:else if param.type === "color"}
    <div class="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        class="h-8 px-3 border-2"
        style="background-color: {value || '#ffffff'}"
        onclick={() => {
          const colorInput = document.getElementById(
            `${param.key}-color-input`
          ) as HTMLInputElement;
          colorInput?.click();
        }}
      >
        <span class="sr-only">选择颜色</span>
      </Button>
      <input
        id="{param.key}-color-input"
        type="color"
        {value}
        oninput={(e) => onValueChange(e.currentTarget.value)}
        class="sr-only"
      />
    </div>
  {:else}
    <!-- 默认文本输入 -->
    <Input
      id={param.key}
      type="text"
      {value}
      oninput={(e) => onValueChange(e.currentTarget.value)}
      class={error ? "border-red-500" : ""}
    />
  {/if}

  <!-- 错误信息显示 -->
  {#if error}
    <p class="text-xs text-red-500">{error}</p>
  {/if}
</div>
